#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音数据抓取器UI启动脚本
检查依赖并启动图形界面
"""

import sys
import os
from pathlib import Path

def check_dependencies():
    """检查必要依赖"""
    missing_deps = []
    
    # 检查PyQt6
    try:
        from PyQt6.QtWidgets import QApplication
        print("[OK] PyQt6 已安装")
    except ImportError:
        missing_deps.append("PyQt6")
        print("[FAIL] PyQt6 未安装")
    
    # 检查DrissionPage
    try:
        from DrissionPage import Chromium
        print("[OK] DrissionPage 已安装")
    except ImportError:
        missing_deps.append("DrissionPage")
        print("[FAIL] DrissionPage 未安装")
    
    # 检查jmespath
    try:
        import jmespath
        print("[OK] jmespath 已安装")
    except ImportError:
        missing_deps.append("jmespath")
        print("[FAIL] jmespath 未安装")
    
    # 检查必要文件
    required_files = ['douyin.py', 'utils.py', 'logger.py', 'config.toml']
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"[OK] {file} 存在")
        else:
            missing_files.append(file)
            print(f"[FAIL] {file} 不存在")
    
    return missing_deps, missing_files

def install_dependencies(missing_deps):
    """安装缺失依赖"""
    if not missing_deps:
        return True
    
    print(f"\n需要安装以下依赖: {', '.join(missing_deps)}")
    
    try:
        import subprocess
        
        for dep in missing_deps:
            print(f"正在安装 {dep}...")
            result = subprocess.run([sys.executable, '-m', 'pip', 'install', dep], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"[OK] {dep} 安装成功")
            else:
                print(f"[FAIL] {dep} 安装失败: {result.stderr}")
                return False
        return True
    
    except Exception as e:
        print(f"[FAIL] 安装过程中出现错误: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("抖音数据抓取器 UI启动器")
    print("=" * 60)
    
    # 检查依赖
    print("\n[检查] 依赖检查...")
    missing_deps, missing_files = check_dependencies()
    
    # 处理缺失文件
    if missing_files:
        print(f"\n[FAIL] 缺失必要文件: {', '.join(missing_files)}")
        print("请确保在正确的目录中运行此脚本")
        return False
    
    # 自动安装缺失依赖
    if missing_deps:
        print(f"\n[WARNING] 发现缺失依赖: {', '.join(missing_deps)}")
        choice = input("是否自动安装缺失依赖? (y/n): ").lower()
        
        if choice == 'y':
            if not install_dependencies(missing_deps):
                print("\n[FAIL] 依赖安装失败，请手动安装")
                print(f"运行命令: pip install {' '.join(missing_deps)}")
                return False
        else:
            print(f"\n请手动安装依赖: pip install {' '.join(missing_deps)}")
            return False
    
    # 启动UI
    print("\n[启动] 启动图形界面...")
    try:
        from ui_main import DouyinScraperMainWindow
        from PyQt6.QtWidgets import QApplication
        
        app = QApplication(sys.argv)
        
        # 设置应用信息
        app.setApplicationName("抖音数据抓取器")
        app.setApplicationVersion("2.0")
        app.setOrganizationName("DouyinScraper")
        
        # 创建主窗口
        main_window = DouyinScraperMainWindow()
        main_window.show()
        
        print("[OK] 图形界面已启动")
        print("[使用说明]:")
        print("   1. 在左侧配置面板输入抖音ID和参数")
        print("   2. 选择要抓取的数据类型")
        print("   3. 点击'开始抓取'按钮")
        print("   4. 查看右侧的进度、结果和日志")
        print("   5. 完成后可导出数据到data目录")
        
        return app.exec()
    
    except Exception as e:
        print(f"[FAIL] 启动失败: {e}")
        print("请检查代码文件是否完整")
        return False

if __name__ == "__main__":
    exit_code = main()
    if exit_code is False:
        sys.exit(1)
    sys.exit(exit_code if isinstance(exit_code, int) else 0)