#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI功能测试脚本
验证界面组件是否正常工作
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch

# 添加当前路径到系统路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试所有必要模块是否能正常导入"""
    print("[测试] 模块导入...")
    
    try:
        # 测试后端模块
        from douyin import DouyinScraper
        from utils import load_config, json_to_csv
        from logger import setup_logger
        print("[OK] 后端模块导入成功")
        
        # 测试PyQt6组件
        from PyQt6.QtWidgets import QApplication, QMainWindow
        from PyQt6.QtCore import QThread, pyqtSignal
        print("[OK] PyQt6核心组件导入成功")
        
        # 测试UI模块
        from ui_main import (
            DouyinScraperMainWindow, ConfigPanel, ControlPanel,
            ProgressPanel, LogPanel, ResultPanel, ScraperWorker
        )
        print("[OK] UI模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"[FAIL] 模块导入失败: {e}")
        return False

def test_config_panel():
    """测试配置面板"""
    print("\n[测试] 配置面板...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from ui_main import ConfigPanel
        
        app = QApplication([])
        
        # 创建配置面板
        config_panel = ConfigPanel()
        
        # 测试获取配置
        config = config_panel.get_config_dict()
        
        # 验证配置结构
        required_keys = ['douyin_id', 'scraper', 'limits', 'optimization', 'concurrent', 'export']
        for key in required_keys:
            if key not in config:
                print(f"[FAIL] 配置缺少键: {key}")
                return False
        
        print("[OK] 配置面板功能正常")
        app.quit()
        return True
        
    except Exception as e:
        print(f"[FAIL] 配置面板测试失败: {e}")
        return False

def test_worker_thread():
    """测试工作线程"""
    print("\n[测试] 工作线程...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from ui_main import ScraperWorker
        
        app = QApplication([])
        
        # 创建测试配置
        test_config = {
            'douyin_id': {'douyin_id': 'test_id'},
            'scraper': {'js_timeout': 10, 'js_retry': 3},
            'limits': {'max_follower_count': 5, 'max_favorite_count': 5}
        }
        
        # 创建工作线程（不实际运行）
        worker = ScraperWorker(test_config, ['user_profile'])
        
        # 验证信号存在
        signals = ['progress_updated', 'status_updated', 'log_message', 
                  'data_received', 'error_occurred', 'finished']
        
        for signal_name in signals:
            if not hasattr(worker, signal_name):
                print(f"[FAIL] 工作线程缺少信号: {signal_name}")
                return False
        
        print("[OK] 工作线程结构正常")
        app.quit()
        return True
        
    except Exception as e:
        print(f"[FAIL] 工作线程测试失败: {e}")
        return False

def test_main_window():
    """测试主窗口"""
    print("\n[测试] 主窗口...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from ui_main import DouyinScraperMainWindow
        
        app = QApplication([])
        
        # 创建主窗口
        main_window = DouyinScraperMainWindow()
        
        # 验证子组件存在
        components = ['config_panel', 'control_panel', 'progress_panel', 
                     'log_panel', 'result_panel']
        
        for component in components:
            if not hasattr(main_window, component):
                print(f"[FAIL] 主窗口缺少组件: {component}")
                return False
        
        print("[OK] 主窗口结构正常")
        app.quit()
        return True
        
    except Exception as e:
        print(f"[FAIL] 主窗口测试失败: {e}")
        return False

def test_ui_functionality():
    """综合功能测试"""
    print("\n[测试] UI综合功能...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from ui_main import DouyinScraperMainWindow
        
        app = QApplication([])
        
        # 创建主窗口
        main_window = DouyinScraperMainWindow()
        
        # 测试配置获取
        config = main_window.config_panel.get_config_dict()
        if not config:
            print("[FAIL] 无法获取配置")
            return False
        
        # 测试日志添加
        main_window.log_panel.add_log("测试日志消息", "INFO")
        
        # 测试进度更新
        main_window.progress_panel.update_progress(50)
        main_window.progress_panel.update_status("测试状态")
        
        # 测试统计更新
        main_window.progress_panel.update_stats({
            'user_count': 1,
            'follower_count': 10,
            'video_count': 5,
            'start_time': '2024-01-01 12:00:00'
        })
        
        print("[OK] UI综合功能正常")
        app.quit()
        return True
        
    except Exception as e:
        print(f"[FAIL] UI综合功能测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("=" * 60)
    print("抖音数据抓取器 UI功能测试")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_imports),
        ("配置面板", test_config_panel),
        ("工作线程", test_worker_thread),
        ("主窗口", test_main_window),
        ("综合功能", test_ui_functionality)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*40}")
        print(f"测试: {test_name}")
        print(f"{'='*40}")
        
        if test_func():
            passed += 1
    
    print(f"\n{'='*60}")
    print(f"测试结果: {passed}/{total} 通过")
    print(f"{'='*60}")
    
    if passed == total:
        print("[SUCCESS] 所有测试通过！UI界面可以正常使用")
        return True
    else:
        print(f"[WARNING] 有 {total - passed} 个测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    
    if success:
        print("\n[OK] 测试完成，可以运行 launch_ui.py 启动界面")
        print("或直接运行: python ui_main.py")
    else:
        print("\n[FAIL] 测试失败，请检查错误信息并修复问题")
    
    sys.exit(0 if success else 1)