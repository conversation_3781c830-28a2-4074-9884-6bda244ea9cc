#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音数据抓取器 - 重构后的主UI界面
基于模块化设计的现代化图形界面
"""

import sys
import os
from datetime import datetime
from typing import Dict, List

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QSplitter, QMessageBox
)
from PyQt6.QtCore import (
    Qt, QThread, pyqtSignal, QTimer, QWaitCondition, QMutex
)
from PyQt6.QtGui import QFont

# 导入重构后的模块
from ui_panels import (
    ConfigPanel, ControlPanel, ProgressPanel, LogPanel, ResultPanel
)
from ui_components import (
    CaptchaDialog, get_modern_window_style
)
from douyin import DouyinScraper
from utils import (
    load_config, is_batch_mode_enabled, get_douyin_ids_from_config,
    BatchProcessingReport, check_privacy_status, generate_batch_filename,
    get_data_file_path, json_to_csv
)
from logger import setup_logger
import main  # 导入批量处理逻辑


class BatchScraperWorker(QThread):
    """批量数据抓取工作线程"""
    
    # 信号定义
    progress_updated = pyqtSignal(int)      # 整体进度更新
    status_updated = pyqtSignal(str)        # 状态更新  
    log_message = pyqtSignal(str, str)      # 日志消息 (message, level)
    batch_progress = pyqtSignal(int, int)   # 批量进度 (current, total)
    current_id_updated = pyqtSignal(str)    # 当前处理ID
    data_received = pyqtSignal(dict)        # 数据接收
    error_occurred = pyqtSignal(str)        # 错误发生
    finished = pyqtSignal()                 # 完成信号
    captcha_required = pyqtSignal(str)      # 滑块验证需求信号
    captcha_completed = pyqtSignal()        # 验证完成响应信号
    user_processing_started = pyqtSignal()  # 用户处理开始信号
    
    def __init__(self, config: Dict, scrape_types: List[str]):
        super().__init__()
        self.config = config
        self.scrape_types = scrape_types
        self.is_running = True
        self.batch_report = BatchProcessingReport()
        
        # 滑块验证等待机制
        self.captcha_wait_condition = QWaitCondition()
        self.captcha_mutex = QMutex()
        self.captcha_user_cancelled = False
        
    def wait_for_captcha_completion(self, scene: str) -> bool:
        """等待用户完成滑块验证"""
        self.log_message.emit(f"检测到滑块验证需求 - 场景: {scene}", "WARNING")
        self.status_updated.emit(f"等待滑块验证: {scene}")
        
        # 发送验证需求信号
        self.captcha_required.emit(scene)
        
        # 等待用户操作
        self.captcha_mutex.lock()
        try:
            # 最多等待5分钟
            timeout_ms = 5 * 60 * 1000
            if not self.captcha_wait_condition.wait(self.captcha_mutex, timeout_ms):
                self.log_message.emit("滑块验证等待超时", "ERROR")
                return False
            
            if self.captcha_user_cancelled:
                self.log_message.emit("用户取消了滑块验证", "WARNING")
                return False
            
            self.log_message.emit("用户完成滑块验证，继续执行", "SUCCESS")
            return True
            
        finally:
            self.captcha_mutex.unlock()
    
    def on_captcha_completed(self, user_cancelled: bool = False):
        """接收验证完成信号"""
        self.captcha_mutex.lock()
        try:
            self.captcha_user_cancelled = user_cancelled
            self.captcha_wait_condition.wakeAll()
        finally:
            self.captcha_mutex.unlock()
        
    def run(self):
        """执行批量抓取任务"""
        try:
            # 检查是否为批量模式
            if not is_batch_mode_enabled(self.config):
                self.run_single_mode()
                return
                
            # 批量模式
            self.run_batch_mode()
            
        except Exception as e:
            self.error_occurred.emit(f"批量抓取失败: {str(e)}")
        finally:
            self.finished.emit()
    
    def run_single_mode(self):
        """单个ID模式运行"""
        try:
            self.status_updated.emit("初始化抓取器...")
            scraper = DouyinScraper(ui_callback=self.wait_for_captcha_completion)
            
            # 使用utils函数获取ID列表，然后取第一个
            douyin_ids = get_douyin_ids_from_config(self.config)
            if not douyin_ids:
                self.error_occurred.emit("请输入抖音ID")
                return
            
            douyin_id = douyin_ids[0]  # 单个模式下取第一个ID
            self.current_id_updated.emit(douyin_id)
            
            # 获取sec_uid
            self.status_updated.emit("获取用户ID...")
            sec_uid = scraper.fetch_sec_uid(douyin_id)
            
            # 执行抓取任务
            self.execute_scrape_tasks(scraper, sec_uid, douyin_id)
            
        except Exception as e:
            self.error_occurred.emit(f"单个模式抓取失败: {str(e)}")
        finally:
            try:
                scraper.close()
            except:
                pass
    
    def run_batch_mode(self):
        """批量模式运行"""
        try:
            douyin_ids = get_douyin_ids_from_config(self.config)
            if not douyin_ids:
                self.error_occurred.emit("批量模式下没有找到有效的抖音ID")
                return
            
            self.batch_progress.emit(0, len(douyin_ids))
            self.log_message.emit(f"开始批量处理 {len(douyin_ids)} 个抖音ID", "INFO")
            
            # 使用main模块的批量处理功能
            success_count = 0
            for i, douyin_id in enumerate(douyin_ids, 1):
                if not self.is_running:
                    break
                    
                self.current_id_updated.emit(douyin_id)
                self.batch_progress.emit(i, len(douyin_ids))
                self.status_updated.emit(f"处理ID {i}/{len(douyin_ids)}: {douyin_id}")
                
                try:
                    # 创建新的抓取器实例
                    scraper = DouyinScraper(ui_callback=self.wait_for_captcha_completion)
                    
                    # 获取sec_uid
                    sec_uid = scraper.fetch_sec_uid(douyin_id)
                    
                    # 执行抓取任务
                    self.execute_scrape_tasks(scraper, sec_uid, douyin_id)
                    success_count += 1
                    
                except Exception as e:
                    self.log_message.emit(f"处理ID {douyin_id} 失败: {str(e)}", "ERROR")
                finally:
                    try:
                        scraper.close()
                    except:
                        pass
                
                # 批量间隔
                if i < len(douyin_ids):
                    import time
                    interval = self.config.get('batch', {}).get('batch_interval_seconds', 5)
                    time.sleep(interval)
            
            self.log_message.emit(f"批量处理完成 - 成功: {success_count}/{len(douyin_ids)}", "SUCCESS")
            
        except Exception as e:
            self.error_occurred.emit(f"批量处理失败: {str(e)}")
    
    def execute_scrape_tasks(self, scraper, sec_uid, douyin_id):
        """执行具体的抓取任务"""
        # 发送用户处理开始信号
        self.user_processing_started.emit()

        stats = {'user_count': 0, 'follower_count': 0, 'video_count': 0}

        # 初始化当前用户数据存储
        self.current_user_data = {}

        # 第一步：始终获取用户资料
        self.status_updated.emit("获取用户信息...")
        user_data = scraper.fetch_user_profile(sec_uid)
        stats['user_count'] = 1

        # 检查隐私状态
        privacy_status = check_privacy_status(user_data)

        # 保存用户数据
        self.current_user_data['user_profile'] = user_data

        self.data_received.emit({
            'type': 'user_profile',
            'data': user_data,
            'douyin_id': douyin_id
        })
        self.log_message.emit("用户信息获取完成", "SUCCESS")

        # 第二步：根据隐私状态和用户选择的任务类型决定执行什么

        # 处理私密账号情况
        if privacy_status['is_private_account']:
            self.log_message.emit(f"用户 {douyin_id} 为私密账号: {privacy_status['private_reason']}", "INFO")
            self.log_message.emit("跳过粉丝列表和喜欢列表获取", "INFO")

            # 为用户选择的任务返回空数据
            if "followers" in self.scrape_types:
                self.data_received.emit({'type': 'followers', 'data': [], 'douyin_id': douyin_id})
            if "favorites" in self.scrape_types:
                self.data_received.emit({'type': 'favorites', 'data': [], 'douyin_id': douyin_id})
            return  # 私密账号直接返回

        # 处理非私密账号的各项任务
        for task_type in self.scrape_types:
            if not self.is_running:
                break

            if task_type == "user_profile":
                # 用户资料已经获取完成，跳过
                continue

            elif task_type == "followers":
                if not privacy_status['followers_accessible']:
                    self.log_message.emit(f"用户 {douyin_id} 粉丝列表不可访问", "INFO")
                    self.data_received.emit({'type': 'followers', 'data': [], 'douyin_id': douyin_id})
                else:
                    self.status_updated.emit("获取粉丝列表...")
                    max_followers = self.config.get('limits', {}).get('max_follower_count', 30)
                    followers_data = scraper.fetch_followers(sec_uid, max_followers)
                    stats['follower_count'] = len(followers_data)

                    # 保存粉丝数据
                    self.current_user_data['followers'] = followers_data

                    self.data_received.emit({
                        'type': 'followers',
                        'data': followers_data,
                        'douyin_id': douyin_id
                    })
                    self.log_message.emit(f"粉丝列表获取完成 - 数量: {len(followers_data)}", "SUCCESS")

            elif task_type == "favorites":
                if not privacy_status['favorites_visible']:
                    self.log_message.emit(f"用户 {douyin_id} 喜欢列表不可见: {privacy_status['favorites_reason']}", "INFO")
                    self.data_received.emit({'type': 'favorites', 'data': [], 'douyin_id': douyin_id})
                else:
                    self.status_updated.emit("获取喜欢列表...")
                    max_favorites = self.config.get('limits', {}).get('max_favorite_count', 20)
                    favorites_data = scraper.fetch_favorites(sec_uid, max_favorites, privacy_check=privacy_status)
                    stats['video_count'] = len(favorites_data)

                    # 保存喜欢列表数据
                    self.current_user_data['favorites'] = favorites_data

                    self.data_received.emit({
                        'type': 'favorites',
                        'data': favorites_data,
                        'douyin_id': douyin_id
                    })
                    self.log_message.emit(f"喜欢列表获取完成 - 数量: {len(favorites_data)}", "SUCCESS")

        # 不再单个保存，改为批量统一保存

    def stop(self):
        """停止抓取"""
        self.is_running = False
        self.log_message.emit("用户停止了抓取任务", "WARNING")


class DouyinScraperMainWindow(QMainWindow):
    """抖音数据抓取器主窗口"""

    def __init__(self):
        super().__init__()
        self.scraper_worker = None
        self.start_time = None

        # 批量数据累积存储
        self.batch_user_profiles = []    # 所有用户基本信息
        self.batch_followers_data = []   # 所有粉丝数据
        self.batch_favorites_data = []   # 所有喜欢列表数据
        self.batch_start_time = None     # 批量任务开始时间
        self.total_users_processed = 0   # 处理的用户总数

        self.init_ui()
        self.setup_connections()

    def is_valid_data(self, data_type: str, data: any, douyin_id: str) -> bool:
        """判断数据是否有效，值得保存"""
        if not data:
            self.log_panel.add_log(f"🔍 {douyin_id} {data_type} 数据为空或None", "DEBUG")
            return False

        if data_type == 'user_profile':
            # 用户信息必须包含基本字段
            required_fields = ['nickname', 'uid', 'sec_uid']

            # 详细检查每个必需字段
            missing_fields = []
            empty_fields = []

            for field in required_fields:
                if field not in data:
                    missing_fields.append(field)
                elif not data[field]:  # 空字符串、None、0等假值
                    empty_fields.append(field)

            # 输出详细的调试信息
            if missing_fields or empty_fields:
                self.log_panel.add_log(f"🔍 {douyin_id} user_profile 验证详情:", "DEBUG")
                self.log_panel.add_log(f"   数据键: {list(data.keys()) if isinstance(data, dict) else 'Not dict'}", "DEBUG")
                if missing_fields:
                    self.log_panel.add_log(f"   缺失字段: {missing_fields}", "DEBUG")
                if empty_fields:
                    self.log_panel.add_log(f"   空值字段: {empty_fields}", "DEBUG")
                for field in required_fields:
                    if field in data:
                        self.log_panel.add_log(f"   {field}: '{data[field]}' (类型: {type(data[field]).__name__})", "DEBUG")
                return False

            return True

        elif data_type in ['followers', 'favorites']:
            # 列表数据必须非空且包含有效记录
            if not isinstance(data, list):
                self.log_panel.add_log(f"🔍 {douyin_id} {data_type} 不是列表类型: {type(data).__name__}", "DEBUG")
                return False
            if len(data) == 0:
                self.log_panel.add_log(f"🔍 {douyin_id} {data_type} 列表为空", "DEBUG")
                return False
            # 检查是否包含有效的记录结构
            invalid_items = [i for i, item in enumerate(data) if not isinstance(item, dict) or not item]
            if invalid_items:
                self.log_panel.add_log(f"🔍 {douyin_id} {data_type} 包含无效记录，索引: {invalid_items[:5]}", "DEBUG")
                return False
            return True

        return False

    def on_data_received(self, data_info):
        """接收并累积数据 - 只保存有效数据"""
        data_type = data_info['type']
        data = data_info['data']
        douyin_id = data_info.get('douyin_id', '')

        # 只有有效数据才累积
        if self.is_valid_data(data_type, data, douyin_id):
            if data_type == 'user_profile':
                self.batch_user_profiles.append({
                    'douyin_id': douyin_id,
                    'data': data,
                    'status': 'success'
                })
                self.log_panel.add_log(f"✅ {douyin_id} 用户信息获取成功", "SUCCESS")

            elif data_type == 'followers':
                self.batch_followers_data.append({
                    'douyin_id': douyin_id,
                    'data': data,
                    'count': len(data),
                    'status': 'success'
                })
                self.log_panel.add_log(f"✅ {douyin_id} 粉丝列表获取成功 ({len(data)}条)", "SUCCESS")

            elif data_type == 'favorites':
                self.batch_favorites_data.append({
                    'douyin_id': douyin_id,
                    'data': data,
                    'count': len(data),
                    'status': 'success'
                })
                self.log_panel.add_log(f"✅ {douyin_id} 喜欢列表获取成功 ({len(data)}条)", "SUCCESS")
        else:
            # 无效数据，记录但不保存
            self.log_panel.add_log(f"❌ {douyin_id} {data_type} 数据无效，跳过保存", "WARNING")

        # 更新结果面板显示（保持原有功能）
        self.result_panel.update_data(data_info)

    def increment_user_count(self):
        """增加处理用户计数"""
        self.total_users_processed += 1

    def save_batch_data_unified(self):
        """批量任务完成后的统一保存 - 只保存成功数据"""
        try:
            import json
            import os

            completion_time = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
            batch_dir = get_data_file_path(completion_time)
            os.makedirs(batch_dir, exist_ok=True)

            files_saved = []
            stats = {
                'user_profiles': 0,
                'followers': 0,
                'favorites': 0
            }

            # 只保存有成功数据的文件
            if self.batch_user_profiles:
                # 保存JSON文件
                profile_file = os.path.join(batch_dir, 'user_profile.json')
                with open(profile_file, 'w', encoding='utf-8') as f:
                    json.dump(self.batch_user_profiles, f, ensure_ascii=False, indent=2)
                files_saved.append('user_profile.json')
                stats['user_profiles'] = len(self.batch_user_profiles)

            if self.batch_followers_data:
                # 保存JSON文件
                followers_file = os.path.join(batch_dir, 'followers_details.json')
                with open(followers_file, 'w', encoding='utf-8') as f:
                    json.dump(self.batch_followers_data, f, ensure_ascii=False, indent=2)
                files_saved.append('followers_details.json')
                stats['followers'] = len(self.batch_followers_data)

                # 保存CSV文件 - 合并所有粉丝数据
                try:
                    all_followers = []
                    for item in self.batch_followers_data:
                        douyin_id = item['douyin_id']
                        for follower in item['data']:
                            # 为每个粉丝记录添加来源用户ID
                            follower_with_source = follower.copy()
                            follower_with_source['来源用户ID'] = douyin_id
                            all_followers.append(follower_with_source)

                    if all_followers:
                        followers_csv = os.path.join(batch_dir, 'followers_details.csv')
                        json_to_csv(all_followers, followers_csv)
                        files_saved.append('followers_details.csv')
                except Exception as e:
                    self.log_panel.add_log(f"⚠️ 粉丝CSV导出失败: {str(e)}", "WARNING")

            if self.batch_favorites_data:
                # 保存JSON文件
                favorites_file = os.path.join(batch_dir, 'favorites_details.json')
                with open(favorites_file, 'w', encoding='utf-8') as f:
                    json.dump(self.batch_favorites_data, f, ensure_ascii=False, indent=2)
                files_saved.append('favorites_details.json')
                stats['favorites'] = len(self.batch_favorites_data)

                # 保存CSV文件 - 合并所有喜欢数据
                try:
                    all_favorites = []
                    for item in self.batch_favorites_data:
                        douyin_id = item['douyin_id']
                        for favorite in item['data']:
                            # 为每个喜欢记录添加来源用户ID
                            favorite_with_source = favorite.copy()
                            favorite_with_source['来源用户ID'] = douyin_id
                            all_favorites.append(favorite_with_source)

                    if all_favorites:
                        favorites_csv = os.path.join(batch_dir, 'favorites_details.csv')
                        json_to_csv(all_favorites, favorites_csv)
                        files_saved.append('favorites_details.csv')
                except Exception as e:
                    self.log_panel.add_log(f"⚠️ 喜欢CSV导出失败: {str(e)}", "WARNING")

            # 详细的保存反馈
            if files_saved:
                self.log_panel.add_log(f"📁 批量数据已保存到: {completion_time}/", "SUCCESS")
                self.log_panel.add_log(f"📊 成功保存: 用户信息{stats['user_profiles']}个, 粉丝列表{stats['followers']}个, 喜欢列表{stats['favorites']}个", "SUCCESS")
                self.log_panel.add_log(f"📄 文件: {', '.join(files_saved)}", "INFO")
            else:
                self.log_panel.add_log("⚠️ 没有有效数据可保存", "WARNING")

        except Exception as e:
            self.log_panel.add_log(f"❌ 批量保存失败: {str(e)}", "ERROR")

    def get_batch_statistics(self):
        """获取批量处理统计信息"""
        return {
            'total_processed': self.total_users_processed,
            'successful_profiles': len(self.batch_user_profiles),
            'successful_followers': len(self.batch_followers_data),
            'successful_favorites': len(self.batch_favorites_data),
            'success_rate': {
                'profiles': len(self.batch_user_profiles) / max(1, self.total_users_processed) * 100,
                'followers': len(self.batch_followers_data) / max(1, self.total_users_processed) * 100,
                'favorites': len(self.batch_favorites_data) / max(1, self.total_users_processed) * 100
            }
        }

    def init_ui(self):
        """初始化UI - 优化三列布局"""
        self.setWindowTitle("抖音数据抓取器 v2.0 - 重构版")
        self.setMinimumSize(1400, 900)  # 增加最小宽度以适应三列布局
        self.resize(1600, 1000)  # 增加默认尺寸
        
        # 应用现代化样式
        self.apply_modern_style()
        
        # 中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局 - 三列水平分割
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建主分割器 - 三列布局
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # === 左列：配置面板 ===
        self.config_panel = ConfigPanel()
        main_splitter.addWidget(self.config_panel)
        
        # === 中列：控制+进度+日志面板 ===
        middle_panel = QWidget()
        middle_layout = QVBoxLayout(middle_panel)
        middle_layout.setContentsMargins(0, 0, 0, 0)
        middle_layout.setSpacing(0)
        
        # 控制面板
        self.control_panel = ControlPanel()
        middle_layout.addWidget(self.control_panel, 0)  # 固定大小
        
        # 进度面板
        self.progress_panel = ProgressPanel()
        self.progress_panel.setMinimumWidth(350)  # 增加最小宽度
        self.progress_panel.setMaximumWidth(450)  # 适当增加最大宽度
        middle_layout.addWidget(self.progress_panel, 0)  # 固定大小
        
        # 日志面板
        self.log_panel = LogPanel()
        middle_layout.addWidget(self.log_panel, 1)  # 可伸缩
        
        main_splitter.addWidget(middle_panel)
        
        # === 右列：结果面板 ===
        self.result_panel = ResultPanel()
        main_splitter.addWidget(self.result_panel)
        
        # 设置三列的初始比例：配置面板:中间面板:结果面板 = 5:3:4
        # 对于1600px宽度：约667px:400px:533px
        main_splitter.setSizes([667, 400, 533])
        
        # 设置分割器属性
        main_splitter.setChildrenCollapsible(False)  # 防止面板被完全折叠
        
        main_layout.addWidget(main_splitter)
    
    def apply_modern_style(self):
        """应用现代化样式"""
        self.setStyleSheet(get_modern_window_style())
    
    def setup_connections(self):
        """设置信号连接"""
        # 控制面板信号
        self.control_panel.start_scraping.connect(self.start_scraping)
        self.control_panel.stop_scraping.connect(self.stop_scraping)
    
    def start_scraping(self, scrape_types: List[str]):
        """开始抓取"""
        try:
            # 获取配置
            config = self.config_panel.get_config_dict()
            
            # 检查配置
            if not config.get('douyin_ids'):
                QMessageBox.warning(self, "警告", "请输入抖音ID！")
                self.control_panel.reset_buttons()
                return
            
            # 构建完整的配置结构，确保与utils.py函数兼容
            full_config = {
                'douyin_id': {
                    'douyin_ids': config['douyin_ids'],
                    'enable_batch_mode': config['enable_batch_mode']
                },
                'scraper': {
                    'js_timeout': config['js_timeout'],
                    'js_retry': config['js_retry']
                },
                'limits': {
                    'max_follower_count': config['max_follower_count'],
                    'max_favorite_count': config['max_favorite_count']
                },
                'optimization': {
                    'optimization_stage': config['optimization_stage']
                },
                'concurrent': {
                    'enable_concurrent_mode': config['enable_concurrent_mode'],
                    'max_concurrent_tabs': config['max_concurrent_tabs']
                },
                'batch': {
                    'batch_interval_seconds': config['batch_interval_seconds'],
                    'id_retry_count': config['id_retry_count'],
                    'skip_failed_ids': config['skip_failed_ids'],
                    'generate_batch_report': config['generate_batch_report']
                }
            }
            
            # 创建工作线程
            self.scraper_worker = BatchScraperWorker(full_config, scrape_types)
            
            # 初始化批量数据累积
            self.batch_user_profiles.clear()
            self.batch_followers_data.clear()
            self.batch_favorites_data.clear()
            self.batch_start_time = datetime.now()
            self.total_users_processed = 0

            # 连接信号
            self.scraper_worker.progress_updated.connect(self.progress_panel.update_progress)
            self.scraper_worker.status_updated.connect(self.progress_panel.update_status)
            self.scraper_worker.log_message.connect(self.log_panel.add_log)
            self.scraper_worker.batch_progress.connect(self.progress_panel.update_batch_progress)
            self.scraper_worker.current_id_updated.connect(self.progress_panel.update_current_id)
            self.scraper_worker.data_received.connect(self.on_data_received)  # 修改为新的累积方法
            self.scraper_worker.error_occurred.connect(self.handle_error)
            self.scraper_worker.finished.connect(self.scraping_finished)
            self.scraper_worker.captcha_required.connect(self.handle_captcha_required)
            self.scraper_worker.user_processing_started.connect(self.increment_user_count)

            # 开始执行
            self.start_time = datetime.now()
            self.progress_panel.update_stats({'start_time': self.start_time.strftime("%H:%M:%S")})
            self.log_panel.add_log("开始抓取任务", "INFO")
            self.scraper_worker.start()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动失败: {str(e)}")
            self.control_panel.reset_buttons()
    
    def stop_scraping(self):
        """停止抓取"""
        if self.scraper_worker and self.scraper_worker.isRunning():
            self.scraper_worker.stop()
            self.log_panel.add_log("正在停止抓取...", "WARNING")
    
    def handle_error(self, error_msg: str):
        """处理错误"""
        self.log_panel.add_log(error_msg, "ERROR")
        QMessageBox.critical(self, "错误", error_msg)
    
    def handle_captcha_required(self, scene: str):
        """处理滑块验证需求"""
        dialog = CaptchaDialog(scene, self)
        result = dialog.exec()
        
        if result == dialog.DialogCode.Accepted:
            success = not dialog.user_cancelled
        else:
            success = False
        
        # 通知工作线程验证结果
        if self.scraper_worker:
            self.scraper_worker.on_captcha_completed(not success)
    
    def scraping_finished(self):
        """抓取完成"""
        self.control_panel.reset_buttons()
        self.progress_panel.update_status("正在保存数据...")

        # 统一保存批量数据
        self.save_batch_data_unified()

        # 显示统计信息
        stats = self.get_batch_statistics()
        self.log_panel.add_log(f"📈 处理统计: 总用户{stats['total_processed']}个", "INFO")
        self.log_panel.add_log(f"📈 成功率: 用户信息{stats['success_rate']['profiles']:.1f}%, 粉丝{stats['success_rate']['followers']:.1f}%, 喜欢{stats['success_rate']['favorites']:.1f}%", "INFO")

        self.progress_panel.update_status("任务完成")

        if self.start_time:
            elapsed = datetime.now() - self.start_time
            self.log_panel.add_log(f"抓取完成，总耗时: {elapsed}", "SUCCESS")


def main():
    """主程序入口"""
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("抖音数据抓取器")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("YourCompany")
    
    # 设置字体
    font = QFont("Microsoft YaHei UI", 9)
    app.setFont(font)
    
    # 创建主窗口
    window = DouyinScraperMainWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec())


if __name__ == "__main__":
    main()