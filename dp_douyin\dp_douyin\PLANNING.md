# 抖音数据抓取器项目规划

## 项目概述
基于 DrissionPage 的抖音网页数据抓取器，支持获取用户粉丝列表、喜欢列表和视频详情。

## 技术栈
- **核心框架**: DrissionPage (浏览器自动化)
- **语言**: Python 3.8+
- **依赖**: jmespath, json, urllib, csv, logging
- **环境**: Windows + PowerShell 7 + Anaconda3

## 项目架构

### 当前结构
```
dp/
├── main.py              # 主程序文件 (1054行)
├── docs/               # 文档目录
├── test/               # 测试目录
└── __pycache__/        # Python缓存
```

### 目标结构 (重构后)
```
dp/
├── main.py              # 主程序入口 (<500行)
├── config.toml          # 配置文件
├── utils.py             # 工具函数
├── logger.py            # 日志模块 (可选)
├── docs/               # 文档目录
├── test/               # 测试目录
└── __pycache__/        # Python缓存
```

## 核心功能模块

### 1. DouyinScraper 类
- **职责**: 抖音数据抓取的核心逻辑
- **主要方法**:
  - `fetch_sec_uid()`: 获取用户 sec_uid
  - `fetch_user_profile()`: 获取用户基本信息
  - `fetch_followers()`: 获取粉丝列表
  - `fetch_favorites()`: 获取喜欢列表
  - `fetch_video_info()`: 获取视频详情

### 2. 配置管理
- **当前**: 硬编码在类变量中
- **目标**: 外置到 config.toml 文件
- **配置项**:
  - 超时设置 (JS_TIMEOUT, JS_RETRY)
  - 延迟设置 (SLEEP_BETWEEN_TRIES, SLEEP_BETWEEN_PAGES)
  - 数量限制 (max_follower_count, max_favorite_count)
  - 导出选项 (json/csv 开关)

### 3. 工具函数
- `json_to_csv()`: JSON 转 CSV 导出
- `get_nested_value()`: 嵌套数据提取
- `setup_logger()`: 日志系统设置

## 设计原则

### 1. 最小化改动
- 保持现有 API 接口不变
- 渐进式重构，每步可回滚
- 向后兼容，不破坏现有功能

### 2. 职责分离
- 配置与逻辑分离
- 工具函数模块化
- 日志系统独立

### 3. 可维护性
- 单文件不超过 500 行
- 清晰的模块划分
- 完整的文档字符串

## 约束条件

### 1. 技术约束
- 必须保持 DrissionPage 兼容性
- 不能破坏现有的网络监听逻辑
- 保持 JSON/CSV 导出功能完整

### 2. 性能约束
- 不能增加额外的网络请求
- 配置加载开销最小化
- 保持原有的重试和延迟机制

### 3. 兼容性约束
- 支持无配置文件运行 (默认值)
- 支持 Python 3.8+ 
- Windows 环境优先

## 风险评估

### 低风险操作
- 创建配置文件
- 添加工具函数模块
- 抽取重复代码

### 中风险操作
- 修改类初始化逻辑
- 重构监听器设置

### 高风险操作
- 修改核心抓取逻辑
- 改变数据结构

## 成功标准

### 功能完整性
- [ ] 所有现有功能正常工作
- [ ] 配置文件正确加载
- [ ] 默认值向后兼容

### 代码质量
- [ ] 单文件不超过 500 行
- [ ] 模块职责清晰
- [ ] 文档字符串完整

### 可维护性
- [ ] 配置外置化完成
- [ ] 重复代码消除
- [ ] 工具函数模块化
