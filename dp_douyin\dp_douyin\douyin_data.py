import time
import datetime
import re
import json
from typing import List, Dict


def fetch_video_info_optimized(scraper, video_id: str) -> Dict:
    """优化版本的视频信息获取，复用已设置的监听器，避免重复设置监听器的性能开销"""
    scraper.logger.debug(f"开始获取视频详情（优化版本） - ID: {video_id}")

    video_url = f"https://www.douyin.com/video/{video_id}"

    # 关键修复：清理监听器队列中的旧数据包，防止数据混乱
    scraper.logger.debug(f"清理监听器队列 - ID: {video_id}")
    cleaned_packets = 0

    # 多轮清理，确保队列完全清空
    for cleanup_round in range(scraper.LISTENER_CLEANUP_ROUNDS):
        round_cleaned = 0
        try:
            # 清空监听器中的旧数据包
            while True:
                try:
                    old_pkt = scraper.dp.listen.wait(timeout=scraper.LISTENER_CLEANUP_TIMEOUT, raise_err=True)
                    round_cleaned += 1
                    cleaned_packets += 1
                    # 记录被清理的数据包信息，便于调试
                    if old_pkt and hasattr(old_pkt, 'response') and old_pkt.response:
                        response_size = len(old_pkt.response.body) if old_pkt.response.body else 0
                        scraper.logger.debug(f"清理旧数据包 - 轮次: {cleanup_round + 1}, 大小: {response_size} bytes")
                except:
                    break  # 没有更多数据包时退出当前轮次
        except Exception as e:
            scraper.logger.debug(f"清理监听器队列异常 - 轮次: {cleanup_round + 1}, 错误: {e}")

        # 如果本轮没有清理到任何数据包，说明队列已空，可以提前结束
        if round_cleaned == 0:
            break

    scraper.logger.debug(f"监听器队列清理完成 - ID: {video_id}, 清理数据包数量: {cleaned_packets}")

    # 直接访问页面，无需重新设置监听器
    scraper.logger.debug(f"访问视频页面: {video_url}")
    scraper.dp.get(video_url)

    # ---- 首包数据获取 ----
    data = None
    last_error = ""

    for retry_count in range(scraper.JS_RETRY):
        try:
            pkt = scraper.dp.listen.wait(timeout=scraper.JS_TIMEOUT, raise_err=True)
            response_size = len(pkt.response.body) if pkt.response.body else 0
            scraper.logger.debug(f"获取响应包 - 大小: {response_size} bytes, 重试次数: {retry_count + 1}")

            data = scraper._to_json(pkt.response.body)
            if (data or {}).get('aweme_detail'):
                # 关键修复：验证获取到的视频ID是否与请求的ID一致
                received_video_id = (data or {}).get('aweme_detail', {}).get('aweme_id', '')

                # 严格数据验证（可配置）
                if scraper.STRICT_DATA_VALIDATION:
                    if received_video_id == video_id:
                        scraper.logger.debug(f"严格验证通过 - 请求ID: {video_id}, 接收ID: {received_video_id}")
                        break
                    else:
                        scraper.logger.warning(f"严格验证失败 - 请求ID: {video_id}, 接收ID: {received_video_id}, 重试: {retry_count + 1}")
                        data = None  # 重置数据，继续重试
                        continue
                else:
                    # 非严格模式，只要有aweme_detail就接受
                    scraper.logger.debug(f"非严格模式获取数据 - 请求ID: {video_id}, 接收ID: {received_video_id}")
                    break
            else:
                scraper.logger.warning(f"响应中无 aweme_detail 字段 - ID: {video_id}, 重试: {retry_count + 1}")
        except Exception as e:
            # 记录最后一次错误，用于后续错误分类
            last_error = str(e)
            error_type = scraper._classify_error(last_error, data)
            scraper.logger.warning(f"获取响应包异常 - ID: {video_id}, 错误类型: {error_type}, 错误: {e}, 重试: {retry_count + 1}")

        if retry_count < scraper.JS_RETRY - 1:
            time.sleep(scraper.SLEEP_BETWEEN_TRIES)

    # 改进的错误判断逻辑：区分JSON解析错误和真正的滑块验证需求
    if not ((data or {}).get('aweme_detail')):
        if scraper._should_trigger_captcha(data, last_error):
            scraper.logger.warning(f"检测到需要滑块验证 - ID: {video_id}")
            scraper._wait_user_to_solve('获取视频信息（首包）')

            for retry_count in range(scraper.JS_RETRY):
                scraper.dp.refresh()
                try:
                    pkt = scraper.dp.listen.wait(timeout=scraper.JS_TIMEOUT, raise_err=True)
                    response_size = len(pkt.response.body) if pkt.response.body else 0
                    scraper.logger.debug(f"验证后获取响应包 - 大小: {response_size} bytes, 重试次数: {retry_count + 1}")

                    data = scraper._to_json(pkt.response.body)
                    if (data or {}).get('aweme_detail'):
                        # 验证后也需要进行ID一致性检查
                        received_video_id = (data or {}).get('aweme_detail', {}).get('aweme_id', '')

                        if scraper.STRICT_DATA_VALIDATION:
                            if received_video_id == video_id:
                                scraper.logger.info(f"验证后严格验证通过 - 请求ID: {video_id}, 接收ID: {received_video_id}")
                                break
                            else:
                                scraper.logger.warning(f"验证后严格验证失败 - 请求ID: {video_id}, 接收ID: {received_video_id}, 重试: {retry_count + 1}")
                                data = None
                                continue
                        else:
                            scraper.logger.info(f"验证后非严格模式获取数据 - 请求ID: {video_id}, 接收ID: {received_video_id}")
                            break
                    else:
                        scraper.logger.warning(f"验证后响应中仍无 aweme_detail 字段 - ID: {video_id}, 重试: {retry_count + 1}")
                except Exception as e:
                    scraper.logger.warning(f"验证后获取响应包异常 - ID: {video_id}, 错误: {e}, 重试: {retry_count + 1}")

                if retry_count < scraper.JS_RETRY - 1:
                    time.sleep(scraper.SLEEP_BETWEEN_TRIES)
        else:
            # 技术错误，不需要人工验证，返回基本信息
            error_type = scraper._classify_error(last_error, data)
            scraper.logger.info(f"检测到技术错误({error_type})，返回基本信息 - ID: {video_id}")
            return get_basic_video_info(video_id)

    video_detail = (data or {}).get('aweme_detail')
    if not video_detail:
        scraper.logger.error(f"多次尝试后仍未获取到视频详情数据 - ID: {video_id}")
        return get_basic_video_info(video_id)

    # 处理视频详情数据（与原方法相同的逻辑）
    return process_video_detail(video_detail, video_id)


def get_basic_video_info(video_id: str) -> Dict:
    """当无法获取详细信息时，返回基本的视频信息"""
    return {
        '视频id': video_id,
        '视频描述': '获取失败',
        '发布时间': '',
        '视频时长': 0,
        '作者UID': '未知',
        '作者昵称': '未知',
        '作者抖音号': '未知',
        '作者签名': '未知',
        '作者获赞数': 0,
        '作者secid': '未知',
        '视频点赞数': 0,
        '视频评论数': 0,
        '视频分享数': 0,
        '视频收藏数': 0,
        '视频链接': f"https://www.douyin.com/video/{video_id}"
    }


def process_video_detail(video_detail: dict, video_id: str) -> Dict:
    """处理视频详情数据，提取需要的字段"""
    video_url = f"https://www.douyin.com/video/{video_id}"

    # 定义需要保留的字段及其路径映射
    keep_fields = {
        '视频id': 'aweme_id',
        '视频描述': 'desc',
        '发布时间': 'create_time',  # 特殊处理：需要格式化
        '视频时长': 'duration',
        '作者UID': 'author.uid',
        '作者昵称': 'author.nickname',
        '作者抖音号': 'author.unique_id',
        '作者签名': 'author.signature',
        '作者获赞数': 'author.favoriting_count',
        '作者secid': 'author.sec_uid',
        '视频点赞数': 'statistics.digg_count',
        '视频评论数': 'statistics.comment_count',
        '视频分享数': 'statistics.share_count',
        '视频收藏数': 'statistics.collect_count',
        '视频链接': None  # 特殊处理：使用构建的URL
    }

    def get_nested_value(obj, path, default=''):
        """安全地获取嵌套字典的值"""
        if not path:
            return default
        try:
            parts = re.split(r'[\.\[\]]', path)
            parts = [p for p in parts if p]
            current = obj
            for part in parts:
                if part.isdigit():
                    current = current[int(part)]
                else:
                    current = current[part]
            return current if current is not None else default
        except (KeyError, TypeError, AttributeError, IndexError):
            return default

    # 处理单个视频对象，提取指定字段
    cleaned_video = {}
    for new_field, path in keep_fields.items():
        if new_field == '视频链接':
            # 特殊处理：使用构建的URL
            cleaned_video[new_field] = video_url
        elif new_field == '发布时间':
            # 特殊处理：将时间戳转换为格式化的日期时间字符串
            create_time = get_nested_value(video_detail, path)
            if create_time:
                try:
                    formatted_time = datetime.datetime.fromtimestamp(int(create_time)).strftime('%Y-%m-%d %H:%M:%S')
                    cleaned_video[new_field] = formatted_time
                except (ValueError, TypeError):
                    cleaned_video[new_field] = ''
            else:
                cleaned_video[new_field] = ''
        else:
            cleaned_video[new_field] = get_nested_value(video_detail, path)

    return cleaned_video


def fetch_favorites(scraper, sec_uid: str, max_items: int = 200) -> List[Dict]:
    """
    获取用户点赞的视频列表，根据配置自动选择串行或并发模式。
    
    Args:
        scraper: DouyinScraper实例
        sec_uid (str): 用户的sec_uid
        max_items (int): 最大获取数量
        
    Returns:
        List[Dict]: 视频详情列表
    """
    
    # 检查是否启用并发模式
    if scraper.ENABLE_CONCURRENT_MODE and scraper.MAX_CONCURRENT_TABS > 1:
        scraper.logger.info(f"检测到并发模式启用 - 并发标签页数: {scraper.MAX_CONCURRENT_TABS}")
        from douyin_concurrent import fetch_favorites_concurrent
        return fetch_favorites_concurrent(scraper, sec_uid, max_items)
    else:
        if not scraper.ENABLE_CONCURRENT_MODE:
            scraper.logger.info("使用串行模式 - 并发模式未启用")
        else:
            scraper.logger.info(f"使用串行模式 - 并发标签页数设置为 {scraper.MAX_CONCURRENT_TABS}")
    
    # 串行模式的现有实现 - 使用优化的监听器管理器
    scraper.listener_manager.setup_listener("/aweme/v1/web/aweme/favorite/?device_platform=webapp")
    like_url = f"https://www.douyin.com/user/{sec_uid}?from_tab_name=main&showTab=like"

    try:
        # 打开点赞页 → 首包
        scraper.dp.get(like_url)

        first_req, data = None, None
        for retry_count in range(scraper.JS_RETRY):
            try:
                pkt = scraper.dp.listen.wait(timeout=scraper.JS_TIMEOUT, raise_err=True)
                first_req = pkt.request.url
                data = scraper._to_json(pkt.response.body)
                if (data or {}).get('aweme_list'):
                    break
                else:
                    scraper.logger.warning(f"首包响应中无 aweme_list 字段, 重试: {retry_count + 1}")
            except Exception as e:
                scraper.logger.warning(f"获取喜欢首包异常: {e}, 重试: {retry_count + 1}")
                data = None

            if retry_count < scraper.JS_RETRY - 1:
                time.sleep(scraper.SLEEP_BETWEEN_TRIES)

        if not ((data or {}).get('aweme_list')):
            scraper._wait_user_to_solve('获取喜欢列表（首包）')
            for retry_count in range(scraper.JS_RETRY):
                scraper.dp.refresh()
                try:
                    pkt = scraper.dp.listen.wait(timeout=scraper.JS_TIMEOUT, raise_err=True)
                    first_req = pkt.request.url
                    data = scraper._to_json(pkt.response.body)
                    if (data or {}).get('aweme_list'):
                        break
                    else:
                        scraper.logger.warning(f"验证后首包响应中仍无 aweme_list 字段, 重试: {retry_count + 1}")
                except Exception as e:
                    scraper.logger.warning(f"验证后获取喜欢首包异常: {e}, 重试: {retry_count + 1}")
                    data = None

                if retry_count < scraper.JS_RETRY - 1:
                    time.sleep(scraper.SLEEP_BETWEEN_TRIES)

        items = (data or {}).get('aweme_list') or []
        if not items:
            raise RuntimeError("多次尝试后仍未获取到喜欢首包数据，请稍后再试。")

        has_more = data.get('has_more', 0)
        cursor = data.get('max_cursor', 0)
        print(f"[首包-喜欢] items={len(items)} has_more={has_more} "
              f"min_cursor={data.get('min_cursor')} max_cursor={data.get('max_cursor')}")

        # 翻页
        while has_more and len(items) < max_items:
            next_url = scraper._set_query_params(first_req, max_cursor=cursor, min_cursor=0)
            page_data = scraper._fetch_json_via_js(next_url, scene='获取喜欢列表（翻页）')

            page_items = (page_data or {}).get('aweme_list') or []
            items.extend(page_items)
            has_more = (page_data or {}).get('has_more', 0)
            cursor = (page_data or {}).get('max_cursor', 0)

            print(f"[喜欢翻页] items={len(page_items)} has_more={has_more} "
                  f"min_cursor={(page_data or {}).get('min_cursor')} "
                  f"max_cursor={(page_data or {}).get('max_cursor')} 累积={len(items)}")

            time.sleep(scraper.SLEEP_BETWEEN_PAGES)

        # 处理视频数据，获取每个视频的详细信息
        def get_nested_value(data, path, default=''):
            """获取嵌套字典中的值，支持点号分隔的路径和数组索引"""
            try:
                current = data
                # 处理路径中的数组索引，如 avatar_thumb.url_list[0]
                import re
                parts = re.split(r'[\.\[\]]', path)
                parts = [p for p in parts if p]  # 移除空字符串

                for part in parts:
                    if part.isdigit():  # 数组索引
                        current = current[int(part)]
                    else:  # 字典键
                        current = current[part]
                return current if current is not None else default
            except (KeyError, TypeError, AttributeError, IndexError):
                return default

        # 获取每个视频的详细信息
        scraper.logger.info(f"开始获取 {len(items)} 个视频的详细信息")
        detailed_favorites = []
        success_count = 0
        fail_count = 0

        # 去重机制：跟踪已处理的视频ID
        processed_video_ids = set() if scraper.ENABLE_DEDUPLICATION else None
        duplicate_count = 0

        # 优化：为批量视频处理设置一次性监听器，避免重复设置（使用监听器管理器）
        scraper.logger.info("设置视频详情监听器（批量处理优化）")
        scraper.listener_manager.setup_listener("/aweme/v1/web/aweme/detail/?device_platform=webapp")

        # 架构性能瓶颈：串行处理架构，无并发优化
        # 总处理时间 = (单视频时间 + 1.5s延迟) × 视频数量
        # 优化建议：使用DrissionPage 4.x的多标签页支持实现有限并发
        for idx, item in enumerate(items, 1):
            video_id = get_nested_value(item, 'aweme_id')
            if video_id:
                # 去重检查
                if scraper.ENABLE_DEDUPLICATION and processed_video_ids is not None:
                    if video_id in processed_video_ids:
                        duplicate_count += 1
                        scraper.logger.debug(f"[{idx}/{len(items)}] 跳过重复视频 - ID: {video_id}")
                        continue
                    processed_video_ids.add(video_id)

                start_time = time.time()
                try:
                    scraper.logger.debug(f"[{idx}/{len(items)}] 开始获取视频详情 - ID: {video_id}")

                    # 性能优化：使用优化版本的视频信息获取，复用已设置的监听器
                    # 获取视频详细信息
                    video_detail = fetch_video_info_optimized(scraper, video_id)
                    detailed_favorites.append(video_detail)

                    elapsed_time = time.time() - start_time
                    success_count += 1

                    scraper.logger.info(f"[{idx}/{len(items)}] 成功获取视频详情 - ID: {video_id}, 耗时: {elapsed_time:.2f}s")
                    print(f"已获取视频 {video_id} 的详细信息 (耗时: {elapsed_time:.2f}s)")

                    # 性能影响：每个视频后强制等待1.5秒
                    # 防风控设计：降低被检测为高频请求的概率
                    # 添加延迟避免请求过快
                    time.sleep(scraper.SLEEP_BETWEEN_PAGES)

                except Exception as e:
                    elapsed_time = time.time() - start_time
                    fail_count += 1

                    scraper.logger.error(f"[{idx}/{len(items)}] 获取视频详情失败 - ID: {video_id}, 错误: {e}, 耗时: {elapsed_time:.2f}s")
                    print(f"获取视频 {video_id} 详情失败: {e} (耗时: {elapsed_time:.2f}s)")

                    # 如果获取详情失败，使用基础信息
                    basic_info = {
                        '视频id': get_nested_value(item, 'aweme_id'),
                        '视频描述': get_nested_value(item, 'desc'),
                        '创建时间': get_nested_value(item, 'create_time'),
                        '视频作者昵称': get_nested_value(item, 'author.nickname'),
                    }
                    detailed_favorites.append(basic_info)
                    continue

        # 添加去重统计信息
        if scraper.ENABLE_DEDUPLICATION and duplicate_count > 0:
            scraper.logger.info(f"去重统计 - 发现并跳过 {duplicate_count} 个重复视频")

        scraper.logger.info(f"视频详情获取完成 - 成功: {success_count}, 失败: {fail_count}, 重复: {duplicate_count}, 总计: {len(items)}")

        return detailed_favorites[:max_items]

    finally:
        scraper.listener_manager.pause_listener()


def fetch_favorites_stage1_optimized(scraper, sec_uid: str, max_items: int = 200, privacy_check: dict = None) -> List[Dict]:
    """阶段一：监听器复用优化 - 预期提升60-70%"""

    # 获取视频列表，传入隐私检查结果
    items = get_video_list(scraper, sec_uid, max_items, privacy_check)

    if not items:  # 如果由于隐私限制返回空列表，直接返回
        return []

    # ⭐ 关键优化：为整个批处理设置一次监听器，使用监听器管理器
    scraper.logger.info("设置视频详情监听器（批量处理优化）")
    scraper.listener_manager.setup_listener("/aweme/v1/web/aweme/detail/?device_platform=webapp")

    detailed_favorites = []
    success_count = 0

    try:
        for idx, item in enumerate(items, 1):
            video_id = item.get('aweme_id')
            if not video_id:
                continue

            start_time = time.time()
            try:
                scraper.logger.debug(f"[{idx}/{len(items)}] 开始获取视频详情 - ID: {video_id}")

                # ⭐ 使用复用监听器的优化版本
                video_detail = fetch_video_info_reuse_listener(scraper, video_id)
                detailed_favorites.append(video_detail)

                elapsed_time = time.time() - start_time
                success_count += 1

                scraper.logger.info(f"[{idx}/{len(items)}] 成功获取视频详情 - ID: {video_id}, 耗时: {elapsed_time:.2f}s")

                # 保持防风控延迟
                time.sleep(scraper.SLEEP_BETWEEN_PAGES)

            except Exception as e:
                elapsed_time = time.time() - start_time
                scraper.logger.error(f"[{idx}/{len(items)}] 获取视频详情失败 - ID: {video_id}, 耗时: {elapsed_time:.2f}s, 错误: {e}")

        scraper.logger.info(f"批量处理完成 - 成功: {success_count}/{len(items)}")
        return detailed_favorites

    finally:
        scraper.listener_manager.stop_listener()  # 使用监听器管理器


def fetch_favorites_stage2_optimized(scraper, sec_uid: str, max_items: int = 200, privacy_check: dict = None) -> List[Dict]:
    """阶段二：监听器复用 + 队列清理机制优化"""

    # 获取视频列表，传入隐私检查结果
    items = get_video_list(scraper, sec_uid, max_items, privacy_check)

    if not items:  # 如果由于隐私限制返回空列表，直接返回
        return []

    # ⭐ 关键优化：为整个批处理设置一次监听器，使用监听器管理器
    scraper.logger.info("设置视频详情监听器（批量处理优化 + 队列清理）")
    scraper.listener_manager.setup_listener("/aweme/v1/web/aweme/detail/?device_platform=webapp")

    detailed_favorites = []
    success_count = 0

    try:
        for idx, item in enumerate(items, 1):
            video_id = item.get('aweme_id')
            if not video_id:
                continue

            start_time = time.time()
            try:
                scraper.logger.debug(f"[{idx}/{len(items)}] 开始获取视频详情 - ID: {video_id}")

                # ⭐ 使用带队列清理的优化版本
                video_detail = fetch_video_info_with_queue_cleanup(scraper, video_id)
                detailed_favorites.append(video_detail)

                elapsed_time = time.time() - start_time
                success_count += 1

                scraper.logger.info(f"[{idx}/{len(items)}] 成功获取视频详情 - ID: {video_id}, 耗时: {elapsed_time:.2f}s")

                # 保持防风控延迟
                time.sleep(scraper.SLEEP_BETWEEN_PAGES)

            except Exception as e:
                elapsed_time = time.time() - start_time
                scraper.logger.error(f"[{idx}/{len(items)}] 获取视频详情失败 - ID: {video_id}, 耗时: {elapsed_time:.2f}s, 错误: {e}")

        scraper.logger.info(f"批量处理完成（阶段二优化） - 成功: {success_count}/{len(items)}")
        return detailed_favorites

    finally:
        scraper.listener_manager.stop_listener()  # 使用监听器管理器


def fetch_favorites_stage3_optimized(scraper, sec_uid: str, max_items: int = 200, privacy_check: dict = None) -> List[Dict]:
    """阶段三：监听器复用 + 队列清理 + 加载模式优化"""

    # 验证加载模式设置
    scraper.logger.info("验证加载模式优化设置")
    verify_load_mode_optimization(scraper)

    # 获取视频列表，传入隐私检查结果
    items = get_video_list(scraper, sec_uid, max_items, privacy_check)

    if not items:  # 如果由于隐私限制返回空列表，直接返回
        return []

    # ⭐ 关键优化：为整个批处理设置一次监听器，使用监听器管理器
    scraper.logger.info("设置视频详情监听器（完整优化：监听器复用 + 队列清理 + 加载模式）")
    scraper.listener_manager.setup_listener("/aweme/v1/web/aweme/detail/?device_platform=webapp")

    detailed_favorites = []
    success_count = 0
    performance_stats = {
        'total_time': 0,
        'avg_time_per_video': 0,
        'queue_cleanups': 0,
        'data_validation_failures': 0
    }

    batch_start_time = time.time()

    try:
        for idx, item in enumerate(items, 1):
            video_id = item.get('aweme_id')
            if not video_id:
                continue

            start_time = time.time()
            try:
                scraper.logger.debug(f"[{idx}/{len(items)}] 开始获取视频详情 - ID: {video_id}")

                # ⭐ 使用完整优化版本
                video_detail = fetch_video_info_with_queue_cleanup(scraper, video_id)
                detailed_favorites.append(video_detail)

                elapsed_time = time.time() - start_time
                success_count += 1
                performance_stats['total_time'] += elapsed_time

                scraper.logger.info(f"[{idx}/{len(items)}] 成功获取视频详情 - ID: {video_id}, 耗时: {elapsed_time:.2f}s")

                # 保持防风控延迟
                time.sleep(scraper.SLEEP_BETWEEN_PAGES)

            except Exception as e:
                elapsed_time = time.time() - start_time
                scraper.logger.error(f"[{idx}/{len(items)}] 获取视频详情失败 - ID: {video_id}, 耗时: {elapsed_time:.2f}s, 错误: {e}")

        # 计算性能统计
        total_batch_time = time.time() - batch_start_time
        if success_count > 0:
            performance_stats['avg_time_per_video'] = performance_stats['total_time'] / success_count

        scraper.logger.info(f"批量处理完成（阶段三完整优化） - 成功: {success_count}/{len(items)}")
        scraper.logger.info(f"性能统计 - 总耗时: {total_batch_time:.2f}s, 平均每视频: {performance_stats['avg_time_per_video']:.2f}s")

        return detailed_favorites

    finally:
        scraper.listener_manager.stop_listener()  # 使用监听器管理器


def fetch_favorites_optimized(scraper, sec_uid: str, max_items: int = 200) -> List[Dict]:
    """
    统一的优化方法，根据配置文件自动选择优化阶段

    优化阶段说明：
    - 阶段1：监听器复用优化（预期提升60-70%）
    - 阶段2：监听器复用 + 队列清理机制
    - 阶段3：监听器复用 + 队列清理 + 加载模式优化（完整优化）
    """

    scraper.logger.info(f"启用优化阶段 {scraper.OPTIMIZATION_STAGE}")

    if scraper.OPTIMIZATION_STAGE == 1:
        scraper.logger.info("使用阶段一优化：监听器复用")
        return fetch_favorites_stage1_optimized(scraper, sec_uid, max_items)
    elif scraper.OPTIMIZATION_STAGE == 2:
        scraper.logger.info("使用阶段二优化：监听器复用 + 队列清理")
        return fetch_favorites_stage2_optimized(scraper, sec_uid, max_items)
    elif scraper.OPTIMIZATION_STAGE == 3:
        scraper.logger.info("使用阶段三优化：完整优化（监听器复用 + 队列清理 + 加载模式）")
        return fetch_favorites_stage3_optimized(scraper, sec_uid, max_items)
    else:
        scraper.logger.warning(f"无效的优化阶段 {scraper.OPTIMIZATION_STAGE}，使用默认的阶段三优化")
        return fetch_favorites_stage3_optimized(scraper, sec_uid, max_items)


def get_video_list(scraper, sec_uid: str, max_items: int, privacy_check: dict = None) -> List[Dict]:
    """
    获取视频列表的辅助方法，支持隐私状态预检查
    
    Args:
        scraper: DouyinScraper实例
        sec_uid: 用户sec_uid
        max_items: 最大获取数量
        privacy_check: 隐私状态检查结果（可选）
    """
    
    # 如果传入了隐私检查结果，先进行权限验证
    if privacy_check and not privacy_check.get('favorites_visible', True):
        scraper.logger.info(f"喜欢列表不可见: {privacy_check.get('favorites_reason', '未知原因')}")
        return []
    
    scraper.listener_manager.setup_listener("/aweme/v1/web/aweme/favorite/?device_platform=webapp")
    like_url = f"https://www.douyin.com/user/{sec_uid}?from_tab_name=main&showTab=like"

    try:
        # 打开点赞页 → 首包
        scraper.dp.get(like_url)

        first_req, data = None, None
        for retry_count in range(scraper.JS_RETRY):
            try:
                pkt = scraper.dp.listen.wait(timeout=scraper.JS_TIMEOUT, raise_err=True)
                first_req = pkt.request.url
                data = scraper._to_json(pkt.response.body)
                if (data or {}).get('aweme_list'):
                    break
                else:
                    scraper.logger.warning(f"首包响应中无 aweme_list 字段, 重试: {retry_count + 1}")
            except Exception as e:
                scraper.logger.warning(f"获取喜欢首包异常: {e}, 重试: {retry_count + 1}")
                data = None

            if retry_count < scraper.JS_RETRY - 1:
                time.sleep(scraper.SLEEP_BETWEEN_TRIES)

        if not ((data or {}).get('aweme_list')):
            scraper._wait_user_to_solve('获取喜欢列表（首包）')
            for retry_count in range(scraper.JS_RETRY):
                scraper.dp.refresh()
                try:
                    pkt = scraper.dp.listen.wait(timeout=scraper.JS_TIMEOUT, raise_err=True)
                    first_req = pkt.request.url
                    data = scraper._to_json(pkt.response.body)
                    if (data or {}).get('aweme_list'):
                        break
                    else:
                        scraper.logger.warning(f"验证后首包响应中仍无 aweme_list 字段, 重试: {retry_count + 1}")
                except Exception as e:
                    scraper.logger.warning(f"验证后获取喜欢首包异常: {e}, 重试: {retry_count + 1}")
                    data = None

                if retry_count < scraper.JS_RETRY - 1:
                    time.sleep(scraper.SLEEP_BETWEEN_TRIES)

        items = (data or {}).get('aweme_list') or []
        if not items:
            raise RuntimeError("多次尝试后仍未获取到喜欢首包数据，请稍后再试。")

        has_more = data.get('has_more', 0)
        cursor = data.get('max_cursor', 0)
        print(f"[首包-喜欢] items={len(items)} has_more={has_more} "
              f"min_cursor={data.get('min_cursor')} max_cursor={data.get('max_cursor')}")

        # 翻页
        while has_more and len(items) < max_items:
            next_url = scraper._set_query_params(first_req, max_cursor=cursor, min_cursor=0)
            page_data = scraper._fetch_json_via_js(next_url, scene='获取喜欢列表（翻页）')

            page_items = (page_data or {}).get('aweme_list') or []
            items.extend(page_items)
            has_more = (page_data or {}).get('has_more', 0)
            cursor = (page_data or {}).get('max_cursor', 0)

            print(f"[喜欢翻页] items={len(page_items)} has_more={has_more} "
                  f"min_cursor={(page_data or {}).get('min_cursor')} "
                  f"max_cursor={(page_data or {}).get('max_cursor')} 累积={len(items)}")

            time.sleep(scraper.SLEEP_BETWEEN_PAGES)

        return items[:max_items]

    finally:
        scraper.listener_manager.pause_listener()


def fetch_video_info_reuse_listener(scraper, video_id: str) -> Dict:
    """复用监听器的优化版本 - 节省1-2秒/视频"""

    video_url = f"https://www.douyin.com/video/{video_id}"

    # ⭐ 关键优化：直接访问页面，无需重新设置监听器
    scraper.dp.get(video_url)

    # 使用推荐的 listen.wait() API
    try:
        packet = scraper.dp.listen.wait(timeout=scraper.JS_TIMEOUT, raise_err=True)
        data = scraper._to_json(packet.response.body)

        if data and data.get('aweme_detail'):
            return process_video_detail(data['aweme_detail'], video_id)
        else:
            scraper.logger.warning(f"响应中无 aweme_detail 字段 - ID: {video_id}")
            return get_basic_video_info(video_id)

    except Exception as e:
        scraper.logger.error(f"获取视频详情异常 - ID: {video_id}, 错误: {e}")
        return get_basic_video_info(video_id)


def fetch_video_info_with_queue_cleanup(scraper, video_id: str) -> Dict:
    """阶段二：添加队列清理机制，确保数据准确性"""

    # ⭐ 队列清理：防止数据包混乱
    cleaned_packets = clean_listener_queue_optimized(scraper)
    scraper.logger.debug(f"清理监听器队列 - ID: {video_id}, 清理数据包数量: {cleaned_packets}")

    video_url = f"https://www.douyin.com/video/{video_id}"
    scraper.dp.get(video_url)

    try:
        packet = scraper.dp.listen.wait(timeout=scraper.JS_TIMEOUT, raise_err=True)
        data = scraper._to_json(packet.response.body)

        if data and data.get('aweme_detail'):
            # ⭐ 严格数据验证：确保获取到的是正确的视频ID
            received_video_id = data.get('aweme_detail', {}).get('aweme_id', '')
            if received_video_id != video_id:
                scraper.logger.warning(f"视频ID不匹配 - 请求: {video_id}, 接收: {received_video_id}")
                return get_basic_video_info(video_id)

            return process_video_detail(data['aweme_detail'], video_id)
        else:
            return get_basic_video_info(video_id)

    except Exception as e:
        scraper.logger.error(f"获取视频详情异常 - ID: {video_id}, 错误: {e}")
        return get_basic_video_info(video_id)


def clean_listener_queue_optimized(scraper) -> int:
    """优化的监听器队列清理方法"""
    cleaned_count = 0
    timeout = scraper.LISTENER_CLEANUP_TIMEOUT / scraper.LISTENER_CLEANUP_ROUNDS  # 分配超时时间

    try:
        for _ in range(scraper.LISTENER_CLEANUP_ROUNDS):
            round_cleaned = 0
            start_time = time.time()

            while time.time() - start_time < timeout:
                try:
                    # 尝试获取队列中的旧数据包
                    old_packet = scraper.dp.listen.wait(timeout=0.1, raise_err=False)
                    if old_packet is None:
                        break
                    round_cleaned += 1
                    cleaned_count += 1

                    # 防止无限循环
                    if round_cleaned > 5:  # 每轮最多清理5个包
                        break
                except:
                    break

            if round_cleaned == 0:
                break  # 如果这轮没有清理到任何包，提前结束

    except Exception as e:
        scraper.logger.debug(f"队列清理过程中出现异常: {e}")

    return cleaned_count


def verify_load_mode_optimization(scraper):
    """验证加载模式优化设置"""
    try:
        # 检查当前加载模式
        # 注意：DrissionPage 4.x 可能没有直接的方法来查询当前加载模式
        # 这里主要是记录优化已启用的信息
        scraper.logger.info("加载模式优化已启用：")
        scraper.logger.info("  - 图片加载已禁用 (no_imgs)")
        scraper.logger.info("  - 加载模式设置为 'none' (发送请求后立即返回)")
        scraper.logger.info("  - 音频已静音 (mute)")
        scraper.logger.info("  - GPU加速已禁用")
        scraper.logger.info("  - 扩展和插件已禁用")
    except Exception as e:
        scraper.logger.warning(f"验证加载模式时出现异常: {e}")