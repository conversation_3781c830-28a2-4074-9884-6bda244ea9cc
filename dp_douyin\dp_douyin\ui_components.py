"""
UI组件模块 - 包含可复用的UI组件和样式
"""

from PyQt6.QtWidgets import (
    QPushButton, QDialog, QVBoxLayout, QHBoxLayout, QLabel
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont


class ModernButton(QPushButton):
    """现代化按钮样式"""
    def __init__(self, text: str, button_type: str = "primary"):
        super().__init__(text)
        self.button_type = button_type
        self.setMinimumHeight(35)
        self.apply_style()
    
    def apply_style(self):
        if self.button_type == "primary":
            self.setStyleSheet("""
                QPushButton {
                    background-color: #2563eb;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-weight: 500;
                }
                QPushButton:hover {
                    background-color: #1d4ed8;
                }
                QPushButton:pressed {
                    background-color: #1e40af;
                }
                QPushButton:disabled {
                    background-color: #9ca3af;
                }
            """)
        elif self.button_type == "success":
            self.setStyleSheet("""
                QPushButton {
                    background-color: #059669;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-weight: 500;
                }
                QPushButton:hover {
                    background-color: #047857;
                }
                QPushButton:pressed {
                    background-color: #065f46;
                }
            """)
        elif self.button_type == "danger":
            self.setStyleSheet("""
                QPushButton {
                    background-color: #dc2626;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-weight: 500;
                }
                QPushButton:hover {
                    background-color: #b91c1c;
                }
                QPushButton:pressed {
                    background-color: #991b1b;
                }
            """)


class CaptchaDialog(QDialog):
    """滑块验证提示对话框"""
    
    def __init__(self, scene: str, parent=None):
        super().__init__(parent)
        self.scene = scene
        self.user_cancelled = False
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("滑块验证")
        self.setModal(True)
        self.setFixedSize(450, 300)
        
        # 设置窗口样式
        self.setStyleSheet("""
            QDialog {
                background-color: #ffffff;
                border: 1px solid #d1d5db;
                border-radius: 8px;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题
        title = QLabel("检测到滑块验证")
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #dc2626;
                text-align: center;
            }
        """)
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 场景描述
        scene_label = QLabel(f"验证场景: {self.scene}")
        scene_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #374151;
                background-color: #f3f4f6;
                padding: 8px 12px;
                border-radius: 6px;
                border: 1px solid #d1d5db;
            }
        """)
        layout.addWidget(scene_label)
        
        # 操作说明
        instructions = QLabel(
            "请按照以下步骤完成验证：\n\n"
            "1. 切换到已打开的浏览器窗口\n"
            "2. 完成页面上的滑块验证\n"
            "3. 验证成功后返回此对话框\n"
            "4. 点击「已完成验证」按钮继续"
        )
        instructions.setStyleSheet("""
            QLabel {
                font-size: 13px;
                color: #4b5563;
                line-height: 1.4;
                padding: 15px;
                background-color: #f8fafc;
                border-radius: 6px;
                border-left: 4px solid #2563eb;
            }
        """)
        layout.addWidget(instructions)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        # 取消按钮
        cancel_btn = ModernButton("取消任务", "danger")
        cancel_btn.clicked.connect(self.on_cancel_clicked)
        button_layout.addWidget(cancel_btn)
        
        # 完成按钮
        complete_btn = ModernButton("已完成验证", "success")
        complete_btn.clicked.connect(self.on_complete_clicked)
        button_layout.addWidget(complete_btn)
        
        layout.addLayout(button_layout)
        
        # 让完成按钮获得焦点
        complete_btn.setFocus()
    
    def on_complete_clicked(self):
        """用户点击完成验证"""
        self.user_cancelled = False
        self.accept()
    
    def on_cancel_clicked(self):
        """用户点击取消任务"""
        self.user_cancelled = True
        self.reject()
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        self.user_cancelled = True
        super().closeEvent(event)


# 样式辅助函数
def get_group_style() -> str:
    """获取分组框样式"""
    return """
        QGroupBox {
            font-weight: bold;
            border: 2px solid #d1d5db;
            border-radius: 8px;
            margin-top: 6px;
            padding-top: 10px;
            background-color: #f9fafb;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 8px 0 8px;
            color: #374151;
            background-color: #f9fafb;
        }
    """


def get_spinbox_style() -> str:
    """获取数字输入框样式"""
    return """
        QSpinBox {
            border: 1px solid #d1d5db;
            border-radius: 4px;
            padding: 6px;
            font-size: 13px;
            background-color: white;
        }
        QSpinBox:focus {
            border-color: #2563eb;
        }
    """


def get_checkbox_style() -> str:
    """获取复选框样式"""
    return """
        QCheckBox {
            font-size: 13px;
            color: #374151;
        }
        QCheckBox::indicator {
            width: 16px;
            height: 16px;
            border: 1px solid #d1d5db;
            border-radius: 3px;
            background-color: white;
        }
        QCheckBox::indicator:checked {
            background-color: #2563eb;
            border-color: #2563eb;
        }
        QCheckBox::indicator:checked::after {
            content: "✓";
            color: white;
            font-weight: bold;
        }
    """


def get_modern_window_style() -> str:
    """获取现代化窗口样式"""
    return """
        QMainWindow {
            background-color: #f8fafc;
        }
        QWidget {
            background-color: #ffffff;
            color: #1f2937;
        }
        QSplitter::handle {
            background-color: #e5e7eb;
        }
        QSplitter::handle:horizontal {
            width: 2px;
        }
        QSplitter::handle:vertical {
            height: 2px;
        }
    """


def get_line_edit_style() -> str:
    """获取单行文本输入框样式"""
    return """
        QLineEdit {
            border: 1px solid #d1d5db;
            border-radius: 4px;
            padding: 6px;
            font-size: 13px;
            background-color: white;
        }
        QLineEdit:focus {
            border-color: #2563eb;
        }
    """


def get_combo_box_style() -> str:
    """获取下拉框样式"""
    return """
        QComboBox {
            border: 1px solid #d1d5db;
            border-radius: 4px;
            padding: 6px;
            font-size: 13px;
            background-color: white;
            min-width: 120px;
        }
        QComboBox:focus {
            border-color: #2563eb;
        }
        QComboBox::drop-down {
            border: none;
            width: 20px;
        }
        QComboBox::down-arrow {
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #6b7280;
            margin-right: 5px;
        }
    """