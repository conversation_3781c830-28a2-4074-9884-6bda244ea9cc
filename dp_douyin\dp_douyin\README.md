# 抖音数据抓取器

基于 DrissionPage 的抖音网页数据抓取器，支持获取用户粉丝列表、喜欢列表和视频详情。

## 功能特性

- 🎯 **用户数据抓取**: 获取用户基本信息、粉丝列表、喜欢列表
- 📊 **多格式导出**: 支持 JSON 和 CSV 格式导出
- ⚙️ **配置化管理**: 通过 config.toml 文件管理所有参数
- 🔄 **智能重试**: 内置重试机制，提高抓取成功率
- 📝 **详细日志**: 完整的日志记录，便于调试和监控

## 项目结构

```
dp/
├── main.py              # 主入口文件 (138行)
├── douyin.py            # DouyinScraper 核心业务逻辑 (750行)
├── utils.py             # 工具函数和配置管理 (200行)
├── logger.py            # 日志系统模块 (38行)
├── config.toml          # 配置文件
├── data/                # 数据导出目录（自动创建）
│   ├── followers_*.json # 粉丝数据文件
│   ├── favorites_*.json # 喜欢数据文件
│   └── *.csv           # CSV 导出文件
├── logs/               # 日志目录（自动创建）
│   └── douyin_scraper_*.log
├── PLANNING.md          # 项目规划文档
├── TASK.md              # 任务记录
├── docs/                # 文档目录
└── test/                # 测试目录
```

## 安装依赖

```bash
pip install DrissionPage jmespath
```

## 配置说明

编辑 `config.toml` 文件来自定义抓取参数：

```toml
[douyin_id]
# 要爬取的抖音ID
douyin_id = "96967475948"

[scraper]
# JavaScript 执行超时时间（秒）
js_timeout = 10
# 超时或返回空时的自动重试次数
js_retry = 3
# 单页内部重试的间隔（秒）
sleep_between_tries = 0.8
# 翻页间隔，降低风控概率（秒）
sleep_between_pages = 1.5
# 视频信息获取首次重试延迟（秒）
video_retry_base_delay = 0.8
# 视频信息获取后续重试额外延迟范围（秒）
video_retry_extra_delay_min = 0.5
video_retry_extra_delay_max = 0.8

[limits]
# 最大粉丝抓取数量
max_follower_count = 30
# 最大喜欢视频抓取数量
max_favorite_count = 30

[export]
# 是否导出粉丝信息到 JSON 文件
follower_json = true
# 是否导出粉丝信息到 CSV 文件
follower_csv = true
# 是否导出喜欢视频信息到 JSON 文件
favorite_json = true
# 是否导出喜欢视频信息到 CSV 文件
favorite_csv = true
```

## 使用方法

1. **基本使用**:
   ```bash
   python main.py
   ```

2. **自定义配置**: 修改 `config.toml` 文件中的参数

3. **无配置文件运行**: 如果没有配置文件，程序会使用默认值

## 核心功能

### DouyinScraper 类

主要方法：
- `fetch_sec_uid(douyin_id)`: 获取用户 sec_uid
- `fetch_user_profile(sec_uid)`: 获取用户基本信息
- `fetch_followers(sec_uid, max_items)`: 获取粉丝列表
- `fetch_favorites(sec_uid, max_items)`: 获取喜欢列表
- `fetch_video_info(video_id)`: 获取视频详情

### 工具函数

`utils.py` 模块提供：
- `json_to_csv()`: JSON 数据转 CSV 文件
- `get_nested_value()`: 安全获取嵌套字典值

## 重构特性

本项目经过精心重构，具有以下特点：

- ✅ **配置外置化**: 所有参数可通过配置文件管理
- ✅ **模块化设计**: 工具函数独立，便于维护
- ✅ **代码复用**: 抽取通用逻辑，减少重复代码
- ✅ **向后兼容**: 无配置文件时使用默认值
- ✅ **渐进式重构**: 不破坏现有功能

## 文件输出

- **数据文件**: 所有 JSON 和 CSV 导出文件自动保存到 `data/` 目录
- **日志文件**: 所有日志文件自动保存到 `logs/` 目录
- **自动创建**: 程序运行时会自动创建所需目录

## 注意事项

1. **浏览器要求**: 需要安装 Chrome 浏览器
2. **网络环境**: 确保能正常访问抖音网站
3. **使用频率**: 建议适当控制抓取频率，避免触发反爬机制
4. **数据用途**: 请遵守相关法律法规，仅用于学习和研究
5. **目录权限**: 确保程序有创建 data 和 logs 目录的权限

## 开发环境

- **系统**: Windows
- **终端**: PowerShell 7
- **Python**: 3.8+
- **环境管理**: Anaconda3

## 更新日志

### 2025-07-28
- ✅ 完成最小化改动重构
- ✅ 实现配置外置化
- ✅ 抽取重复代码逻辑
- ✅ 工具函数模块化
- ✅ 模块化拆分优化
- ✅ 目录管理优化（data/logs目录）
- ✅ 抖音ID配置化
- ✅ 视频重试延迟优化（智能延迟机制）
- ✅ 所有文件行数控制在合理范围
- ✅ 保持向后兼容性
