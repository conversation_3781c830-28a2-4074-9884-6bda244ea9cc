# 抖音数据抓取器 - 图形界面版

基于Qt6的现代化图形界面，为抖音数据抓取器提供直观易用的操作体验。

## 界面预览

### 主要功能区域
- **左侧配置面板**: 抖音ID设置、抓取参数配置、导出选项
- **右上结果区域**: 实时进度显示、数据结果预览、统计信息
- **右下日志区域**: 实时日志输出、操作记录

### 核心特性
- 🎨 **现代化设计**: 采用扁平化设计风格，界面美观简洁
- 📊 **实时监控**: 进度条、状态显示、统计信息实时更新
- 📝 **详细日志**: 彩色日志输出，支持不同级别的消息显示
- 📈 **数据预览**: 表格形式展示抓取结果，支持多标签页切换
- 💾 **一键导出**: 支持JSON和CSV格式导出，自动保存到data目录
- ⚙️ **配置管理**: 图形化配置界面，无需手动编辑配置文件

## 快速开始

### 1. 环境要求
```bash
# Python 3.8+
# 依赖包：
pip install PyQt6 DrissionPage jmespath
```

### 2. 启动方式

#### 方式一：使用启动器（推荐）
```bash
python launch_ui.py
```
启动器会自动检查依赖并提供安装选项。

#### 方式二：直接启动
```bash
python ui_main.py
```

#### 方式三：测试后启动
```bash
# 先运行功能测试
python test_ui.py
# 测试通过后启动
python ui_main.py
```

## 使用指南

### 基础配置
1. **抖音ID设置**: 在左侧配置面板输入目标用户的抖音ID
2. **抓取限制**: 设置最大粉丝数量和喜欢视频数量
3. **超时参数**: 根据网络情况调整JS执行超时时间和重试次数

### 高级配置
- **性能优化**: 选择优化阶段（1-3级）
- **并发模式**: 启用多标签页并发处理
- **导出格式**: 选择JSON和/或CSV导出格式

### 操作流程
1. **配置设置** → 输入抖音ID和调整参数
2. **选择类型** → 勾选要抓取的数据类型（用户信息/粉丝/喜欢）
3. **开始抓取** → 点击"开始抓取"按钮
4. **监控进度** → 查看右侧进度条和日志输出
5. **查看结果** → 在结果预览区域查看抓取到的数据
6. **导出数据** → 点击"导出数据"按钮保存结果

## 界面组件说明

### 配置面板 (ConfigPanel)
- **滚动支持**: 添加了垂直滚动功能，解决内容过多时显示不完整的问题
- **基础配置**: 抖音ID、超时设置、重试次数
- **抓取限制**: 最大粉丝数量、最大喜欢数量
- **性能优化**: 优化阶段、并发模式、并发标签页数
- **导出设置**: 导出格式选择（JSON/CSV）
- **响应式布局**: 更好的空间分配和视觉层次

### 控制面板 (ControlPanel)  
- **抓取类型**: 用户信息、粉丝列表、喜欢列表
- **控制按钮**: 开始抓取、停止抓取

### 进度面板 (ProgressPanel)
- **执行状态**: 当前操作状态显示
- **进度条**: 可视化进度指示
- **统计信息**: 已抓取用户、粉丝、视频数量
- **时间信息**: 开始时间显示

### 日志面板 (LogPanel)
- **实时日志**: 彩色分级日志输出
- **日志级别**: INFO（蓝色）、SUCCESS（绿色）、WARNING（黄色）、ERROR（红色）
- **自动滚动**: 新日志自动滚动到底部
- **清除功能**: 一键清除所有日志

### 结果面板 (ResultPanel)
- **多标签页**: 用户信息、粉丝列表、喜欢列表分别显示
- **表格预览**: 结构化数据表格展示
- **导出功能**: 一键导出所有数据到本地

## 文件结构

```
dp_douyin/
├── ui_main.py          # 主UI界面文件
├── launch_ui.py        # UI启动器
├── test_ui.py          # UI功能测试
├── main.py             # 命令行版本（原版）
├── douyin.py           # 核心抓取逻辑
├── utils.py            # 工具函数
├── logger.py           # 日志系统
├── config.toml         # 配置文件
├── data/               # 数据导出目录
└── logs/               # 日志文件目录
```

## 技术特性

### UI框架
- **PyQt6**: 现代化跨平台GUI框架
- **信号槽机制**: 实现组件间通信
- **线程安全**: 使用QThread处理后台任务

### 设计模式
- **MVC架构**: 分离UI、逻辑和数据
- **模块化组件**: 每个面板独立封装
- **响应式布局**: 支持窗口大小调整

### 样式系统
- **现代化主题**: 基于Tailwind CSS色彩方案
- **自定义组件**: ModernButton等现代化控件
- **响应式设计**: 适配不同分辨率屏幕

## 故障排除

### 常见问题

1. **导入错误**
   ```
   ImportError: No module named 'PyQt6'
   ```
   解决：运行 `pip install PyQt6`

2. **配置文件读取失败**
   - 确保 `config.toml` 文件存在
   - 检查文件权限和格式

3. **抓取失败**
   - 检查网络连接
   - 验证抖音ID是否正确
   - 适当增加超时时间

4. **界面显示异常**
   - 检查屏幕分辨率设置
   - 尝试重启应用程序

### 调试模式
在代码中设置 `enable_debug_logging = true` 开启详细日志输出。

## 开发说明

### 扩展界面
如需添加新功能，可以：
1. 创建新的面板类继承 `QWidget`
2. 在主窗口中添加对应组件
3. 连接必要的信号槽

### 自定义样式
修改各组件的 `setStyleSheet()` 调用来自定义外观。

### 性能优化
- 使用 QThread 处理耗时操作
- 限制日志显示行数
- 实现数据分页加载

## 版本历史

- **v2.0**: 图形界面版本发布
  - 完整的Qt6图形界面
  - 实时进度监控
  - 多线程处理
  - 现代化UI设计

- **v1.0**: 命令行版本
  - 核心抓取功能
  - 配置文件支持
  - 性能优化

## 许可证

本项目仅用于学习和研究目的，请遵守相关法律法规。

## 技术支持

如遇问题，请查看：
1. `test_ui.py` 运行结果
2. `logs/` 目录下的详细日志
3. 控制台错误输出信息