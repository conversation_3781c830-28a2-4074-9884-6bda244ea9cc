from __future__ import annotations

from DrissionPage import Chromium, ChromiumOptions
import jmespath
import json
from urllib.parse import urlsplit, parse_qsl, urlencode, urlunsplit
import time
import csv
import logging
import re
from datetime import datetime
from typing import List, Dict, Set, Union
# 导入工具函数
from utils import (
    json_to_csv, get_nested_value, load_config, get_data_file_path,
    get_douyin_ids_from_config, is_batch_mode_enabled, 
    generate_batch_filename, BatchProcessingReport, check_privacy_status
)
from logger import setup_logger
from douyin import DouyinScraper

def process_single_douyin_id(douyin_id: str, config: dict, scraper: DouyinScraper, logger, naming_pattern: str = "id_timestamp") -> dict:
    """
    处理单个抖音ID的数据抓取任务。
    
    Args:
        douyin_id (str): 抖音ID
        config (dict): 配置字典
        scraper (DouyinScraper): 抓取器实例
        logger: 日志记录器
        naming_pattern (str): 文件命名模式
        
    Returns:
        dict: 处理结果，包含成功状态和详情
    """
    start_time = time.time()
    result = {
        'douyin_id': douyin_id,
        'success': False,
        'error': None,
        'processing_time': 0,
        'files_generated': [],
        'data': {  # 新增数据字段用于UI显示
            'user_profile': None,
            'followers': None,
            'favorites': None
        }
    }
    
    try:
        logger.info(f"开始处理用户 - 抖音ID: {douyin_id}")
        
        # 从配置文件读取参数
        limits_config = config.get('limits', {})
        export_config = config.get('export', {})
        
        Max_follower_count = limits_config.get('max_follower_count', 30)
        Max_favorite_count = limits_config.get('max_favorite_count', 30)
        
        # 导出选项
        is_export_follower_json = export_config.get('follower_json', True)
        is_export_follower_csv = export_config.get('follower_csv', True)
        is_export_favorite_json = export_config.get('favorite_json', True)
        is_export_favorite_csv = export_config.get('favorite_csv', True)
        
        logger.info(f"配置参数 - 最大粉丝数: {Max_follower_count}, 最大喜欢数: {Max_favorite_count}")
        
        # 获取sec_uid
        sec_uid = scraper.fetch_sec_uid(douyin_id)
        if not sec_uid:
            raise Exception(f"无法获取用户 {douyin_id} 的 sec_uid，请检查抖音ID是否正确")
        logger.info(f"获取到 sec_uid: {sec_uid}")
        
        # 获取用户基本信息
        logger.info("开始获取用户基本信息")
        profile = scraper.fetch_user_profile(sec_uid)
        result['data']['user_profile'] = profile  # 保存到结果中
        
        # 检查隐私状态
        privacy_status = check_privacy_status(profile)
        result['privacy_status'] = privacy_status
        
        # 显示基本信息（排除隐私字段）
        for k, v in profile.items():
            if k not in ['secret', 'user_not_show', 'special_state_info', 'show_favorite_list', 'favorite_permission']:
                print(f"{k}: {v}")
        print('=' * 60)
        logger.info("用户基本信息获取完成")
        
        # 隐私状态处理
        if privacy_status['is_private_account']:
            logger.info(f"用户 {douyin_id} 为私密账号: {privacy_status['private_reason']}")
            logger.info("跳过粉丝列表和喜欢列表获取")
            print(f"\n[隐私提示] 该用户为私密账号（{privacy_status['private_reason']}），无法获取粉丝和喜欢列表")
            
            result['data']['followers'] = []
            result['data']['favorites'] = []
            
            # 直接标记为成功并返回
            result['success'] = True
            result['processing_time'] = time.time() - start_time
            logger.info(f"用户 {douyin_id} 处理完成（私密账号），耗时: {result['processing_time']:.2f}秒")
            return result
        
        # 非私密账号，进行具体列表获取
        
        # 粉丝列表获取
        if privacy_status['followers_accessible']:
            try:
                followers = scraper.fetch_followers(
                    sec_uid=sec_uid,
                    max_items=Max_follower_count,
                    page_count=20
                )
                result['data']['followers'] = followers  # 保存到结果中
                print(f"\n粉丝抓取完成：{len(followers)} 条。")
                
                # 保存粉丝数据
                if is_export_follower_json:
                    json_filename = get_data_file_path(
                        generate_batch_filename(douyin_id, "followers_details", naming_pattern) + ".json"
                    )
                    with open(json_filename, 'w', encoding='utf-8') as f:
                        json.dump(followers, f, ensure_ascii=False, indent=2)
                    print(f"粉丝详情已保存到 JSON 文件: {json_filename} (共{len(followers)}条记录)")
                    result['files_generated'].append(json_filename)
                    
                if is_export_follower_csv:
                    csv_filename = get_data_file_path(
                        generate_batch_filename(douyin_id, "followers_details", naming_pattern) + ".csv"
                    )
                    json_to_csv(followers, csv_filename)
                    result['files_generated'].append(csv_filename)
            except Exception as e:
                logger.warning(f"粉丝列表获取失败: {e}")
                result['data']['followers'] = []
                print(f"\n[提示] 粉丝列表获取失败，可能由于权限限制")
        else:
            logger.info(f"用户 {douyin_id} 粉丝列表不可访问")
            result['data']['followers'] = []
            print(f"\n[提示] 该用户粉丝列表不可访问")
        
        # 喜欢列表获取
        if privacy_status['favorites_visible']:
            logger.info("开始获取喜欢列表")
            favorites = scraper.fetch_favorites(
                sec_uid=sec_uid,
                max_items=Max_favorite_count
            )
            result['data']['favorites'] = favorites  # 保存到结果中
            print(f"\n喜欢列表抓取完成：{len(favorites)} 条。")
            
            # 保存喜欢列表数据
            if is_export_favorite_json:
                json_filename = get_data_file_path(
                    generate_batch_filename(douyin_id, "favorites_details", naming_pattern) + ".json"
                )
                with open(json_filename, 'w', encoding='utf-8') as f:
                    json.dump(favorites, f, ensure_ascii=False, indent=2)
                print(f"喜欢列表已保存到 JSON 文件: {json_filename} (共{len(favorites)}条记录)")
                result['files_generated'].append(json_filename)
                
            if is_export_favorite_csv:
                csv_filename = get_data_file_path(
                    generate_batch_filename(douyin_id, "favorites_details", naming_pattern) + ".csv"
                )
                json_to_csv(favorites, csv_filename)
                result['files_generated'].append(csv_filename)
        else:
            logger.info(f"用户 {douyin_id} 喜欢列表不可见: {privacy_status['favorites_reason']}")
            result['data']['favorites'] = []
            print(f"\n[提示] 该用户喜欢列表不可见（{privacy_status['favorites_reason']}）")
        
        result['success'] = True
        result['processing_time'] = time.time() - start_time
        
        logger.info(f"用户 {douyin_id} 处理完成，耗时: {result['processing_time']:.2f}秒")
        return result
        
    except Exception as e:
        result['error'] = str(e)
        result['processing_time'] = time.time() - start_time
        logger.error(f"用户 {douyin_id} 处理失败: {e}", exc_info=True)
        return result


def process_batch_douyin_ids(douyin_ids: List[str], config: dict) -> None:
    """
    批量处理多个抖音ID。
    
    Args:
        douyin_ids (List[str]): 抖音ID列表
        config (dict): 配置字典
    """
    logger = setup_logger("BatchMain", logging.INFO)
    logger.info("=" * 60)
    logger.info("抖音数据批量抓取程序启动")
    logger.info("=" * 60)
    
    # 获取批量处理配置
    batch_config = config.get('batch', {})
    batch_interval = batch_config.get('batch_interval_seconds', 5)
    id_retry_count = batch_config.get('id_retry_count', 2)
    skip_failed_ids = batch_config.get('skip_failed_ids', True)
    generate_report = batch_config.get('generate_batch_report', True)
    naming_pattern = batch_config.get('output_naming_pattern', 'id_timestamp')
    
    logger.info(f"批量处理配置 - 间隔: {batch_interval}s, 重试: {id_retry_count}次, 跳过失败: {skip_failed_ids}")
    logger.info(f"计划处理 {len(douyin_ids)} 个抖音ID")
    
    # 初始化批量处理报告
    report = BatchProcessingReport() if generate_report else None
    
    # 创建抓取器实例
    scraper = DouyinScraper()
    
    try:
        for idx, douyin_id in enumerate(douyin_ids, 1):
            logger.info(f"\n[{idx}/{len(douyin_ids)}] 开始处理抖音ID: {douyin_id}")
            print(f"\n[处理进度] {idx}/{len(douyin_ids)} - 当前ID: {douyin_id}")
            
            # 尝试处理当前ID（包含重试逻辑）
            success = False
            last_error = None
            
            for retry in range(id_retry_count + 1):
                if retry > 0:
                    logger.info(f"第 {retry} 次重试处理 {douyin_id}")
                    
                try:
                    result = process_single_douyin_id(douyin_id, config, scraper, logger, naming_pattern)
                    
                    if result['success']:
                        success = True
                        if report:
                            report.add_success(douyin_id, result['processing_time'])
                        
                        print(f"[成功] {douyin_id} 处理成功 - 耗时: {result['processing_time']:.2f}s")
                        print(f"   生成文件: {len(result['files_generated'])} 个")
                        for file_path in result['files_generated']:
                            print(f"   文件: {file_path}")
                        break
                    else:
                        last_error = result['error']
                        if retry < id_retry_count:
                            logger.warning(f"处理 {douyin_id} 失败，准备重试: {last_error}")
                            time.sleep(2)  # 重试前等待
                        
                except Exception as e:
                    last_error = str(e)
                    logger.error(f"处理 {douyin_id} 时发生异常: {e}")
                    if retry < id_retry_count:
                        time.sleep(2)  # 重试前等待
            
            # 处理失败的情况
            if not success:
                print(f"[失败] {douyin_id} 处理失败: {last_error}")
                if report:
                    report.add_failure(douyin_id, last_error or "未知错误")
                
                if not skip_failed_ids:
                    logger.error(f"ID {douyin_id} 处理失败，停止批量处理")
                    break
                else:
                    logger.warning(f"ID {douyin_id} 处理失败，跳过并继续处理下一个")
            
            # 如果不是最后一个ID，等待间隔时间
            if idx < len(douyin_ids):
                logger.info(f"等待 {batch_interval} 秒后处理下一个ID...")
                time.sleep(batch_interval)
        
        # 生成并保存批量处理报告
        if report:
            report.print_summary()
            if generate_report:
                report_filename = report.save_report()
                logger.info(f"批量处理报告已保存: {report_filename}")
                
    except KeyboardInterrupt:
        logger.info("用户中断批量处理")
        if report:
            report.print_summary()
    except Exception as e:
        logger.error(f"批量处理过程中发生异常: {e}", exc_info=True)
    finally:
        # scraper.close()  # 如果需要的话
        logger.info("=" * 60)
        logger.info("抖音数据批量抓取程序结束")
        logger.info("=" * 60)


def main():
    """主函数：根据配置选择单个或批量处理模式。"""
    # 加载配置
    config = load_config()
    
    # 获取抖音ID列表
    douyin_ids = get_douyin_ids_from_config(config)
    
    if not douyin_ids:
        print("[错误] 未找到有效的抖音ID")
        print("请在 config.toml 文件中配置 douyin_ids 列表")
        return
    
    # 检查是否启用批量模式
    batch_mode = is_batch_mode_enabled(config)
    
    if batch_mode and len(douyin_ids) > 1:
        print(f"[批量模式] 启用批量处理模式，将处理 {len(douyin_ids)} 个抖音ID")
        process_batch_douyin_ids(douyin_ids, config)
    else:
        # 兼容模式：仅处理第一个ID
        single_id = douyin_ids[0]
        print(f"[兼容模式] 仅处理第一个抖音ID: {single_id}")
        
        # 使用原有的单ID处理逻辑
        logger = setup_logger("Main", logging.INFO)
        logger.info("=" * 60)
        logger.info("抖音数据抓取程序启动")
        logger.info("=" * 60)
        
        scraper = DouyinScraper()
        try:
            result = process_single_douyin_id(single_id, config, scraper, logger)
            if result['success']:
                print(f"[成功] 处理完成 - 耗时: {result['processing_time']:.2f}秒")
            else:
                print(f"[失败] 处理失败: {result['error']}")
        except Exception as e:
            logger.error(f"程序执行出现异常: {e}", exc_info=True)
            raise
        finally:
            # scraper.close()
            logger.info("=" * 60)
            logger.info("抖音数据抓取程序结束")
            logger.info("=" * 60)
            print(f"任务完成")



if __name__ == "__main__":
    main()
